import { Link } from 'react-router-dom';

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-midnight">
      <nav className="border-b border-white/10 px-4 py-3">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <Link to="/" className="text-xl font-bold">
              Synapsetron
            </Link>
            <Link to="/admin" className="text-sm text-snow/70 hover:text-snow">
              Admin
            </Link>
          </div>
        </div>
      </nav>
      <main className="container mx-auto">{children}</main>
    </div>
  );
}
