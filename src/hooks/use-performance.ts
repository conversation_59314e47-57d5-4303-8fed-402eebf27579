import { useAppStore } from '@/stores/app-store';
import { useCallback, useEffect, useRef } from 'react';

interface PerformanceEntry {
  name: string;
  startTime: number;
  duration: number;
  entryType: string;
}

interface WebVitalsMetrics {
  CLS: number; // Cumulative Layout Shift
  FID: number; // First Input Delay
  FCP: number; // First Contentful Paint
  LCP: number; // Largest Contentful Paint
  TTFB: number; // Time to First Byte
}

export const usePerformanceMonitoring = () => {
  const updatePerformanceMetrics = useAppStore((state) => state.updatePerformanceMetrics);
  const trackEvent = useAppStore((state) => state.trackEvent);
  const optimizePerformance = useAppStore((state) => state.optimizePerformance);

  const metricsRef = useRef<Partial<WebVitalsMetrics>>({});
  const observerRef = useRef<PerformanceObserver | null>(null);

  // Measure component render time
  const measureRender = useCallback(
    (componentName: string) => {
      const startTime = performance.now();

      return () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;

        updatePerformanceMetrics({ renderTime });
        trackEvent('component_render', {
          componentName,
          renderTime,
          timestamp: Date.now(),
        });

        // Auto-optimize if render time is too high
        if (renderTime > 16) {
          console.warn(`Slow render detected for ${componentName}: ${renderTime.toFixed(2)}ms`);
          optimizePerformance();
        }
      };
    },
    [updatePerformanceMetrics, trackEvent, optimizePerformance]
  );

  // Measure user interaction latency
  const measureInteraction = useCallback(
    (interactionType: string) => {
      const startTime = performance.now();

      return () => {
        const endTime = performance.now();
        const latency = endTime - startTime;

        updatePerformanceMetrics({ interactionLatency: latency });
        trackEvent('user_interaction', {
          type: interactionType,
          latency,
          timestamp: Date.now(),
        });
      };
    },
    [updatePerformanceMetrics, trackEvent]
  );

  // Monitor memory usage
  const monitorMemory = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB

      updatePerformanceMetrics({ memoryUsage });

      // Warn if memory usage is high
      if (memoryUsage > 100) {
        console.warn(`High memory usage detected: ${memoryUsage.toFixed(2)}MB`);
        trackEvent('high_memory_usage', { memoryUsage });
      }
    }
  }, [updatePerformanceMetrics, trackEvent]);

  // Web Vitals monitoring
  const initWebVitals = useCallback(() => {
    // First Contentful Paint
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcp = entries.find((entry) => entry.name === 'first-contentful-paint');
      if (fcp) {
        metricsRef.current.FCP = fcp.startTime;
        trackEvent('web_vital', { metric: 'FCP', value: fcp.startTime });
      }
    });
    fcpObserver.observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        metricsRef.current.LCP = lastEntry.startTime;
        trackEvent('web_vital', { metric: 'LCP', value: lastEntry.startTime });
      }
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (entry.processingStart && entry.startTime) {
          const fid = entry.processingStart - entry.startTime;
          metricsRef.current.FID = fid;
          trackEvent('web_vital', { metric: 'FID', value: fid });
        }
      });
    });
    fidObserver.observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      metricsRef.current.CLS = clsValue;
      trackEvent('web_vital', { metric: 'CLS', value: clsValue });
    });
    clsObserver.observe({ entryTypes: ['layout-shift'] });

    return () => {
      fcpObserver.disconnect();
      lcpObserver.disconnect();
      fidObserver.disconnect();
      clsObserver.disconnect();
    };
  }, [trackEvent]);

  // Navigation timing
  const measureNavigationTiming = useCallback(() => {
    if ('getEntriesByType' in performance) {
      const navigation = performance.getEntriesByType(
        'navigation'
      )[0] as PerformanceNavigationTiming;

      if (navigation) {
        const loadTime = navigation.loadEventEnd - navigation.fetchStart;
        const ttfb = navigation.responseStart - navigation.fetchStart;

        updatePerformanceMetrics({ loadTime });
        metricsRef.current.TTFB = ttfb;

        trackEvent('navigation_timing', {
          loadTime,
          ttfb,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
          domComplete: navigation.domComplete - navigation.fetchStart,
        });
      }
    }
  }, [updatePerformanceMetrics, trackEvent]);

  // Resource timing
  const monitorResourceTiming = useCallback(() => {
    const resources = performance.getEntriesByType('resource');
    let totalSize = 0;

    resources.forEach((resource: any) => {
      if (resource.transferSize) {
        totalSize += resource.transferSize;
      }
    });

    updatePerformanceMetrics({ bundleSize: totalSize / 1024 }); // KB

    trackEvent('resource_timing', {
      totalResources: resources.length,
      totalSize: totalSize / 1024,
      timestamp: Date.now(),
    });
  }, [updatePerformanceMetrics, trackEvent]);

  // Performance budget monitoring
  const checkPerformanceBudget = useCallback(() => {
    const budgets = {
      loadTime: 3000, // 3 seconds
      renderTime: 16, // 16ms for 60fps
      memoryUsage: 50, // 50MB
      bundleSize: 1000, // 1MB
    };

    const current = useAppStore.getState().performance;
    const violations: string[] = [];

    if (current.loadTime > budgets.loadTime) {
      violations.push(`Load time: ${current.loadTime}ms > ${budgets.loadTime}ms`);
    }

    if (current.renderTime > budgets.renderTime) {
      violations.push(`Render time: ${current.renderTime}ms > ${budgets.renderTime}ms`);
    }

    if (current.memoryUsage > budgets.memoryUsage) {
      violations.push(`Memory usage: ${current.memoryUsage}MB > ${budgets.memoryUsage}MB`);
    }

    if (current.bundleSize > budgets.bundleSize) {
      violations.push(`Bundle size: ${current.bundleSize}KB > ${budgets.bundleSize}KB`);
    }

    if (violations.length > 0) {
      console.warn('Performance budget violations:', violations);
      trackEvent('performance_budget_violation', { violations });
    }
  }, [trackEvent]);

  // Initialize performance monitoring
  useEffect(() => {
    const cleanup = initWebVitals();
    measureNavigationTiming();

    // Set up periodic monitoring
    const memoryInterval = setInterval(monitorMemory, 10000); // Every 10 seconds
    const resourceInterval = setInterval(monitorResourceTiming, 30000); // Every 30 seconds
    const budgetInterval = setInterval(checkPerformanceBudget, 60000); // Every minute

    return () => {
      cleanup();
      clearInterval(memoryInterval);
      clearInterval(resourceInterval);
      clearInterval(budgetInterval);
    };
  }, [
    initWebVitals,
    measureNavigationTiming,
    monitorMemory,
    monitorResourceTiming,
    checkPerformanceBudget,
  ]);

  // Export performance data
  const exportPerformanceData = useCallback(() => {
    const state = useAppStore.getState();
    const events = state.analytics?.events || [];
    const performanceData = {
      metrics: state.performance,
      webVitals: metricsRef.current,
      events: events.filter(
        (e) =>
          e.type.includes('performance') ||
          e.type.includes('render') ||
          e.type.includes('interaction')
      ),
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      connection: (navigator as any).connection
        ? {
            effectiveType: (navigator as any).connection.effectiveType,
            downlink: (navigator as any).connection.downlink,
            rtt: (navigator as any).connection.rtt,
          }
        : null,
    };

    return performanceData;
  }, []);

  return {
    measureRender,
    measureInteraction,
    monitorMemory,
    checkPerformanceBudget,
    exportPerformanceData,
    webVitals: metricsRef.current,
  };
};

// Hook for component-level performance monitoring
export const useComponentPerformance = (componentName: string) => {
  const { measureRender } = usePerformanceMonitoring();
  const renderEndRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    renderEndRef.current = measureRender(componentName);

    return () => {
      if (renderEndRef.current) {
        renderEndRef.current();
      }
    };
  });

  return {
    startMeasurement: () => {
      renderEndRef.current = measureRender(componentName);
    },
    endMeasurement: () => {
      if (renderEndRef.current) {
        renderEndRef.current();
        renderEndRef.current = null;
      }
    },
  };
};
