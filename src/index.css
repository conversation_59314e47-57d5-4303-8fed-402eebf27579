@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100..800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ultra-modern CSS custom properties for theming */
@layer base {
  :root {
    /* Core theme colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Primary brand colors */
    --primary: 160 84% 39%;
    --primary-foreground: 210 40% 98%;
    --primary-50: 151 81% 96%;
    --primary-100: 154 91% 88%;
    --primary-200: 156 82% 77%;
    --primary-300: 158 74% 65%;
    --primary-400: 159 69% 52%;
    --primary-500: 160 84% 39%;
    --primary-600: 161 94% 30%;
    --primary-700: 163 94% 24%;
    --primary-800: 163 88% 20%;
    --primary-900: 164 86% 16%;
    --primary-950: 166 91% 9%;

    /* Secondary colors */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    /* Muted colors */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Accent colors */
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    /* Destructive colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Border and input colors */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 160 84% 39%;

    /* Border radius */
    --radius: 0.75rem;

    /* Shadows */
    --shadow-color: 220 3% 15%;
    --shadow-strength: 1%;
  }

  .dark {
    /* Dark theme colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Primary colors in dark mode */
    --primary: 160 84% 39%;
    --primary-foreground: 222.2 84% 4.9%;

    /* Secondary colors in dark mode */
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    /* Muted colors in dark mode */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    /* Accent colors in dark mode */
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    /* Destructive colors in dark mode */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    /* Border and input colors in dark mode */
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 160 84% 39%;

    /* Shadows in dark mode */
    --shadow-color: 220 40% 2%;
    --shadow-strength: 25%;
  }

  /* Neural theme variant */
  .theme-neural {
    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;
    --accent: 217 91% 60%;
    --ring: 217 91% 60%;
  }

  /* Quantum theme variant */
  .theme-quantum {
    --primary: 292 84% 61%;
    --primary-foreground: 210 40% 98%;
    --accent: 292 84% 61%;
    --ring: 292 84% 61%;
  }
}

/* Base styles with modern defaults */
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Modern scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
  }

  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  /* Modern form elements */
  input[type="search"]::-webkit-search-decoration,
  input[type="search"]::-webkit-search-cancel-button,
  input[type="search"]::-webkit-search-results-button,
  input[type="search"]::-webkit-search-results-decoration {
    display: none;
  }
}

/* Ultra-modern component styles */
@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-xl border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-xl border border-white/10;
  }

  /* Neural network inspired gradients */
  .neural-bg {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--primary) / 0.8) 50%,
      hsl(var(--primary) / 0.6) 100%
    );
  }

  .quantum-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  }

  /* Modern button variants */
  .btn-neural {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium px-6 py-3 rounded-xl;
    @apply hover:from-blue-600 hover:to-purple-700 transition-all duration-300;
    @apply focus:ring-2 focus:ring-blue-500/50 focus:outline-none;
    @apply active:scale-95 transform;
  }

  .btn-quantum {
    @apply bg-gradient-to-r from-purple-500 to-pink-600 text-white font-medium px-6 py-3 rounded-xl;
    @apply hover:from-purple-600 hover:to-pink-700 transition-all duration-300;
    @apply focus:ring-2 focus:ring-purple-500/50 focus:outline-none;
    @apply active:scale-95 transform;
  }

  /* Glow effects */
  .glow-primary {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }

  .glow-neural {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
  }

  .glow-quantum {
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.4);
  }

  /* Text gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent;
  }

  .text-gradient-neural {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent;
  }

  .text-gradient-quantum {
    @apply bg-gradient-to-r from-purple-500 to-pink-600 bg-clip-text text-transparent;
  }

  /* Loading states */
  .loading-shimmer {
    background: linear-gradient(
      90deg,
      hsl(var(--muted)) 25%,
      hsl(var(--muted-foreground) / 0.1) 50%,
      hsl(var(--muted)) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Modern card styles */
  .card-modern {
    @apply bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-6;
    @apply shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .card-neural {
    @apply card-modern border-blue-500/20 hover:border-blue-500/40;
    @apply hover:shadow-neural;
  }

  .card-quantum {
    @apply card-modern border-purple-500/20 hover:border-purple-500/40;
    @apply hover:shadow-quantum;
  }
}

/* Ultra-modern utility classes */
@layer utilities {
  /* Spacing utilities */
  .space-y-fluid > * + * {
    margin-top: clamp(1rem, 2.5vw, 2rem);
  }

  .space-x-fluid > * + * {
    margin-left: clamp(1rem, 2.5vw, 2rem);
  }

  /* Typography utilities */
  .text-fluid-sm {
    font-size: clamp(0.875rem, 1.5vw, 1rem);
  }

  .text-fluid-base {
    font-size: clamp(1rem, 2vw, 1.125rem);
  }

  .text-fluid-lg {
    font-size: clamp(1.125rem, 2.5vw, 1.25rem);
  }

  .text-fluid-xl {
    font-size: clamp(1.25rem, 3vw, 1.5rem);
  }

  .text-fluid-2xl {
    font-size: clamp(1.5rem, 4vw, 2rem);
  }

  .text-fluid-3xl {
    font-size: clamp(1.875rem, 5vw, 2.5rem);
  }

  .text-fluid-4xl {
    font-size: clamp(2.25rem, 6vw, 3rem);
  }

  /* Animation utilities */
  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slide-in-left 0.6s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slide-in-right 0.6s ease-out forwards;
  }

  /* Performance utilities */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Accessibility utilities */
  .sr-only-focusable:not(:focus):not(:focus-within) {
    @apply sr-only;
  }

  /* Modern layout utilities */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  /* Container queries support */
  .container-normal {
    container-type: normal;
  }

  .container-inline-size {
    container-type: inline-size;
  }

  .container-size {
    container-type: size;
  }
}

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --primary: 142 84% 50%;
    --primary-foreground: 144.9 80.4% 10%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142 84% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-midnight text-snow;
  }
}

html {
  scroll-behavior: smooth;
}

.glow-effect {
  box-shadow: 0 0 15px theme("colors.jungle");
}

.network-node {
  width: 4px;
  height: 4px;
  background: theme("colors.jungle");
  border-radius: 50%;
  position: absolute;
  animation: glow 2s infinite;
}

.network-line {
  height: 1px;
  background: linear-gradient(90deg, transparent, theme("colors.jungle"), transparent);
  position: absolute;
  transform-origin: left;
}
