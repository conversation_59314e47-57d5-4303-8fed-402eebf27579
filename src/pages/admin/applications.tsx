import { getJoinApplications } from '@/lib/supabase';
import type { JoinApplication } from '@/types/join';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function ApplicationsAdmin() {
  const [applications, setApplications] = useState<JoinApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedId, setExpandedId] = useState<number | null>(null);

  useEffect(() => {
    const loadApplications = async () => {
      try {
        const data = await getJoinApplications();
        setApplications(data);
      } catch (error) {
        console.error('Error loading applications:', error);
      } finally {
        setLoading(false);
      }
    };

    loadApplications();
  }, []);

  if (loading) return <div>Loading...</div>;

  const toggleExpand = (id: number) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Join Applications</h1>
      <div className="grid gap-4">
        {applications.map((app) => (
          <div key={app.id} className="border border-white/10 rounded-lg p-4">
            <div
              className="flex justify-between items-start mb-4 cursor-pointer"
              onClick={() => toggleExpand(app.id)}
            >
              <div>
                <h2 className="text-xl font-semibold">{app.full_name}</h2>
                <p className="text-sm text-gray-400">{formatDate(app.created_at)}</p>
              </div>
              <div className="flex items-center gap-3">
                <span
                  className={`px-3 py-1 rounded-full text-sm ${
                    app.status === 'pending'
                      ? 'bg-yellow-500/20 text-yellow-500'
                      : app.status === 'approved'
                        ? 'bg-green-500/20 text-green-500'
                        : 'bg-red-500/20 text-red-500'
                  }`}
                >
                  {app.status}
                </span>
                {expandedId === app.id ? (
                  <ChevronUp className="w-5 h-5" />
                ) : (
                  <ChevronDown className="w-5 h-5" />
                )}
              </div>
            </div>

            {expandedId === app.id && (
              <div className="grid gap-6 pt-4 border-t border-white/10">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-400">Email</label>
                    <p>{app.email}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Phone</label>
                    <p>{app.phone || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Website</label>
                    <p>
                      {app.website ? (
                        <a
                          href={app.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-emerald-500 hover:underline"
                        >
                          {app.website}
                        </a>
                      ) : (
                        'N/A'
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Experience</label>
                    <p>{app.experience || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Position</label>
                    <p>
                      {app.job_title} at {app.company}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Engagement Type</label>
                    <p>{app.engagement_type}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm text-gray-400">Service Interest</label>
                  <p>{app.service_interest}</p>
                </div>

                <div>
                  <label className="text-sm text-gray-400">Motivation</label>
                  <p className="whitespace-pre-wrap">{app.motivation || 'N/A'}</p>
                </div>

                <div>
                  <label className="text-sm text-gray-400">AI/LLM Experience</label>
                  <p className="whitespace-pre-wrap">{app.ai_experience || 'N/A'}</p>
                </div>

                {app.projects && (
                  <div>
                    <label className="text-sm text-gray-400">Projects</label>
                    <p className="whitespace-pre-wrap">{app.projects}</p>
                  </div>
                )}

                {app.goals && (
                  <div>
                    <label className="text-sm text-gray-400">Goals</label>
                    <p className="whitespace-pre-wrap">{app.goals}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
