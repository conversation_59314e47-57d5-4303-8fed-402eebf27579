import { motion } from 'framer-motion';
import { ArrowLeft } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';
import PricingCard from '../components/PricingCard';

const pricingPlans = [
  {
    title: 'Starter',
    price: 'R250',
    annualPrice: 'R2400',
    savePercentage: 20,
    description: 'For individuals & small businesses looking for a simple online presence.',
    features: [
      'AI-powered chatbot (basic)',
      'Custom domain name',
      'Secure hosting',
      'Mobile-responsive design',
      '1x - Free Monthly maintenance & updates',
    ],
    featureDetails: {
      'AI-powered chatbot (basic)': 'Handles common customer queries with predetermined responses',
      'Custom domain name': 'Get a professional web address and email for your business',
    },
    recommendation: 'Perfect for personal sites & small business pages.',
  },
  {
    title: 'Pro',
    price: 'R750',
    annualPrice: 'R7200',
    savePercentage: 20,
    description: 'For growing businesses that need more engagement & automation.',
    features: [
      'Everything in Starter, plus:',
      'AI chatbot with knowledge base integration',
      'Contact forms & lead capture',
      'Admin content management system',
      'Enhanced security features',
      '3x - Free Monthly maintenance & updates',
    ],
    featureDetails: {
      'AI chatbot with knowledge base integration (RAG)':
        'Integrates with your knowledge base to provide accurate responses',
      'Contact forms & lead capture': 'Capture leads and inquiries directly from your website',
    },
    recommendation: 'Ideal for startups & service providers.',
    isPopular: true,
  },
  {
    title: 'Business',
    price: 'R1500',
    annualPrice: 'R14400',
    savePercentage: 20,
    description: 'For businesses requiring custom functionality & scalability.',
    features: [
      'Everything in Pro, plus:',
      'AI chatbot advanced support',
      'Advanced SEO optimization',
      'User management & authentication',
      'Payment gateway integration',
      'Basic Analytics & reporting',
      '5x - Free Monthly maintenance & updates',
    ],
    featureDetails: {
      'AI chatbot advanced support': 'Advanced AI chatbot with machine learning capabilities',
      'Advanced SEO optimization': 'Improve your website’s visibility on search engines',
      'User management & authentication': 'Manage user accounts and access permissions',
      'Payment gateway integration': 'Accept online payments directly on your website',
    },
    recommendation: 'Great for growing businesses, online stores, booking sites.',
  },
  {
    title: 'Enterprise',
    price: 'Custom',
    description: 'For enterprises needing fully tailored solutions.',
    features: [
      'Everything in Business, plus:',
      'Fully custom AI chatbot with Automation capabilities',
      'Dedicated server & cloud infrastructure',
      'Priority support & monitoring',
      'Enterprise-grade security & compliance',
    ],
    featureDetails: {
      'Fully custom AI chatbot with deep learning capabilities':
        'Tailored AI chatbot with advanced learning capabilities',
      'Dedicated server & cloud infrastructure':
        'Exclusive server and cloud resources for your business',
      'Custom integrations & automation':
        'Integrate with your existing systems and automate workflows',
    },
    recommendation: 'Designed for high-traffic & large-scale operations.',
  },
];

const faqs = [
  {
    question: 'How does the AI chatbot work?',
    answer:
      "Our AI chatbot uses advanced natural language processing to understand and respond to your visitors' queries in real-time, providing accurate and contextual responses based on your website's content.",
  },
  {
    question: 'Can I upgrade or downgrade my plan?',
    answer:
      'Yes, you can change your plan at any time. The changes will be reflected in your next billing cycle.',
  },
  {
    question: 'What kind of support do you offer?',
    answer:
      'We provide email support for all plans, with priority support and 24/7 monitoring for Enterprise customers.',
  },
];

const ModernizePricing = () => {
  return (
    <div className="min-h-screen bg-midnight text-snow">
      <Link to="/">
        <motion.div
          className="fixed top-8 left-8 z-50 flex items-center gap-2 px-4 py-2 rounded-lg 
                     bg-white/5 hover:bg-white/10 border border-white/20 backdrop-blur-md
                     text-snow/80 hover:text-emerald-500 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </motion.div>
      </Link>

      <div className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 text-transparent bg-clip-text">
                Pricing & Plans
              </span>
            </h1>
            <p className="text-xl text-snow/80 max-w-3xl mx-auto">
              Build intelligent websites and integrate AI-driven customer support for an enhanced
              user experience.
            </p>
          </div>

          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="inline-block bg-emerald-500/10 border border-emerald-500/20 rounded-lg px-6 py-3"
            >
              <span className="text-emerald-500 font-semibold">
                30-Day Money-Back Guarantee • No Credit Card Required
              </span>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {pricingPlans.map((plan, index) => (
              <motion.div
                key={plan.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <PricingCard {...plan} />
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12 mb-32">
            <p className="text-snow/60 text-sm">
              Not sure which plan is right for you?{' '}
              <a href="#" className="text-emerald-500 hover:text-emerald-400 underline">
                Compare features in detail
              </a>{' '}
              or{' '}
              <a href="#" className="text-emerald-500 hover:text-emerald-400 underline">
                schedule a demo
              </a>
            </p>
            <p className="text-snow/60 text-sm mt-4">
              Interested in automation services?{' '}
              <Link
                to="/services/automation"
                className="text-emerald-500 hover:text-emerald-400 underline"
              >
                Learn more about our automation services
              </Link>
            </p>
          </div>

          <div className="mt-32">
            <h2 className="text-3xl font-bold text-center mb-12">
              <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 text-transparent bg-clip-text">
                Frequently Asked Questions
              </span>
            </h2>
            <div className="max-w-3xl mx-auto space-y-6">
              {faqs.map((faq, index) => (
                <div
                  key={index}
                  className="bg-white/5 backdrop-blur-md border border-white/20 rounded-lg p-6 hover:bg-white/10 transition-colors"
                >
                  <h3 className="text-xl font-semibold mb-3 text-emerald-500">{faq.question}</h3>
                  <p className="text-snow/80">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernizePricing;
