import { motion } from 'framer-motion';
import { ArrowLeft } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';
import PricingCard from '../components/PricingCard';

const pricingPlans = [
  {
    title: 'Starter AI',
    price: 'R5000',
    annualPrice: 'R48000',
    savePercentage: 20,
    description: 'For businesses starting their AI journey with basic ML/AI integration needs.',
    features: [
      'Pre-trained model integration',
      'Basic API implementation',
      'Data preprocessing setup',
      'Model performance monitoring',
      '5 hours monthly consultation',
    ],
    featureDetails: {
      'Pre-trained model integration': 'Integrate existing AI models into your applications',
      'Basic API implementation': 'REST API endpoints for model interaction',
    },
    recommendation: 'Perfect for businesses starting with AI integration.',
  },
  {
    title: 'Advanced AI',
    price: 'R15000',
    annualPrice: 'R144000',
    savePercentage: 20,
    description: 'Custom AI solutions and advanced model development.',
    features: [
      'Everything in Starter AI, plus:',
      'Custom model development',
      'Advanced data pipeline setup',
      'Model fine-tuning & optimization',
      'Automated testing & deployment',
      '15 hours monthly consultation',
    ],
    featureDetails: {
      'Custom model development': 'Build specialized AI models for your needs',
      'Model fine-tuning': 'Optimize model performance for your use case',
    },
    recommendation: 'Ideal for businesses needing specialized AI solutions.',
    isPopular: true,
  },
  {
    title: 'Enterprise AI',
    price: 'R35000',
    annualPrice: 'R336000',
    savePercentage: 20,
    description: 'Full-scale AI infrastructure and solution development.',
    features: [
      'Everything in Advanced AI, plus:',
      'Distributed AI infrastructure',
      'MLOps implementation',
      'Advanced monitoring & scaling',
      'Custom AI pipeline development',
      'Dedicated AI development team',
      '40 hours monthly consultation',
    ],
    featureDetails: {
      'Distributed AI infrastructure': 'Scale your AI solutions across clusters',
      'MLOps implementation': 'Automated ML lifecycle management',
    },
    recommendation: 'For organizations requiring comprehensive AI solutions.',
  },
  {
    title: 'Research & Innovation',
    price: 'Custom',
    description: 'Cutting-edge AI research and development projects.',
    features: [
      'Novel AI algorithm development',
      'Research paper collaboration',
      'Patent development support',
      'Custom hardware integration',
      'Unlimited consultation',
    ],
    featureDetails: {
      'Novel AI algorithm development': 'Develop new AI approaches and methods',
      'Research collaboration': 'Work with our AI research team',
    },
    recommendation: 'For organizations pushing AI boundaries.',
  },
];

const faqs = [
  {
    question: 'What types of AI models can you develop?',
    answer:
      'We specialize in various AI domains including natural language processing, computer vision, reinforcement learning, and custom neural networks.',
  },
  {
    question: 'How long does AI model development take?',
    answer:
      'Timeline varies based on complexity - from 2-4 weeks for basic implementations to several months for custom solutions.',
  },
  {
    question: 'Do you provide model maintenance?',
    answer: 'Yes, all our plans include ongoing monitoring and maintenance of deployed AI models.',
  },
];

const AIDevelopmentPricing = () => {
  return (
    <div className="min-h-screen bg-midnight text-snow">
      <Link to="/">
        <motion.div
          className="fixed top-8 left-8 z-50 flex items-center gap-2 px-4 py-2 rounded-lg 
                     bg-white/5 hover:bg-white/10 border border-white/20 backdrop-blur-md
                     text-snow/80 hover:text-emerald-500 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </motion.div>
      </Link>

      <div className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 text-transparent bg-clip-text">
                AI Development Solutions
              </span>
            </h1>
            <p className="text-xl text-snow/80 max-w-3xl mx-auto">
              Transform your business with custom AI solutions and cutting-edge machine learning
              models.
            </p>
          </div>

          {/* Rest of the component structure matches ModernizePricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {pricingPlans.map((plan, index) => (
              <motion.div
                key={plan.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <PricingCard {...plan} />
              </motion.div>
            ))}
          </div>

          <div className="mt-32">
            <h2 className="text-3xl font-bold text-center mb-12">
              <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 text-transparent bg-clip-text">
                Frequently Asked Questions
              </span>
            </h2>
            <div className="max-w-3xl mx-auto space-y-6">
              {faqs.map((faq, index) => (
                <div
                  key={index}
                  className="bg-white/5 backdrop-blur-md border border-white/20 rounded-lg p-6 hover:bg-white/10 transition-colors"
                >
                  <h3 className="text-xl font-semibold mb-3 text-emerald-500">{faq.question}</h3>
                  <p className="text-snow/80">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIDevelopmentPricing;
