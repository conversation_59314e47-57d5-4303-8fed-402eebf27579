import { AnimatePresence, motion } from 'framer-motion';
import { ArrowLeft, Book, Brain, Calculator, Search, Sparkles, Wrench, X } from 'lucide-react';
import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';

const Education = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const sections = [
    {
      title: 'Learn Python',
      icon: <Book className="w-6 h-6 text-emerald-500" />,
      resources: [
        {
          name: 'Introduction to Python',
          link: 'https://cs50.harvard.edu/python/2022/',
          description: "Harvard's CS50 Python course for beginners",
        },
        {
          name: 'AI with Python',
          link: 'https://www.edx.org/learn/artificial-intelligence/harvard-university-cs50-s-introduction-to-artificial-intelligence-with-python',
          description: "Harvard's introduction to AI using Python",
        },
        {
          name: 'NumPy',
          link: 'https://numpy.org/devdocs/user/quickstart.html',
          description: 'Essential numerical computing library',
        },
        {
          name: 'Matplotlib',
          link: 'https://matplotlib.org/stable/tutorials/index.html',
          description: 'Data visualization fundamentals',
        },
        {
          name: 'Scikit-learn',
          link: 'https://scikit-learn.org/1.4/tutorial/index.html',
          description: 'Machine learning implementation',
        },
      ],
    },
    {
      title: 'Math Foundations',
      icon: <Calculator className="w-6 h-6 text-emerald-500" />,
      resources: [
        {
          name: 'Linear Algebra',
          link: 'https://ocw.mit.edu/courses/mathematics/18-06-linear-algebra-spring-2010/',
          description: "MIT's comprehensive linear algebra course",
        },
        {
          name: 'Probability & Statistics',
          link: 'https://web.stanford.edu/class/stats116/syllabus.html',
          description: "Stanford's probability fundamentals",
        },
        {
          name: 'Calculus',
          link: 'https://www.khanacademy.org/math/multivariable-calculus',
          description: "Khan Academy's multivariable calculus",
        },
      ],
    },
    {
      title: 'Build Practical Experience',
      icon: <Brain className="w-6 h-6 text-emerald-500" />,
      resources: [
        {
          name: 'Hands-On Machine Learning',
          link: 'https://www.oreilly.com/library/view/hands-on-machine-learning/9781492032632/',
          description: 'Comprehensive guide to ML with Scikit-Learn, Keras, and TensorFlow',
        },
        {
          name: 'fast.ai Deep Learning',
          link: 'https://course.fast.ai/',
          description: 'Practical Deep Learning for Coders with real-world applications',
        },
        {
          name: 'ML Project Management',
          link: 'https://www.coursera.org/learn/machine-learning-projects',
          description: 'Learn to structure and manage ML projects effectively',
        },
        {
          name: 'Build GPT From Scratch',
          link: 'https://www.youtube.com/watch?v=kCc8FmEb1nY',
          description: "Andrej Karpathy's tutorial on building a GPT model from ground up",
        },
      ],
    },
    {
      title: 'Deepen Knowledge',
      icon: <Brain className="w-6 h-6 text-emerald-500" />,
      resources: [
        {
          name: 'NLP Course',
          link: 'https://huggingface.co/learn/nlp-course/chapter1/1',
          description: 'Learn Natural Language Processing with Hugging Face',
        },
        {
          name: 'Deep Reinforcement Learning',
          link: 'https://huggingface.co/learn/deep-rl-course/unit0/introduction',
          description: 'Master Deep RL fundamentals and implementation',
        },
        {
          name: 'Computer Vision',
          link: 'https://www.kaggle.com/learn/computer-vision',
          description: 'Practical computer vision techniques on Kaggle',
        },
        {
          name: 'MIT Computer Vision',
          link: 'https://www.youtube.com/watch?v=vT1JzLTH4G4&list=PLSVEhWrZWDHQTBmWZufjxpw3s8sveJtnJ&index=1',
          description: 'Comprehensive computer vision course from MIT',
        },
        {
          name: 'Advanced NLP Techniques',
          link: 'https://huggingface.co/learn/nlp-course/chapter1/1',
          description: 'Advanced concepts and practices in NLP',
        },
      ],
    },
  ];

  const filteredSections = useMemo(() => {
    return sections
      .map((section) => ({
        ...section,
        resources: section.resources.filter((resource) => {
          const matchesSearch =
            resource.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            resource.description.toLowerCase().includes(searchQuery.toLowerCase());
          const matchesCategory =
            selectedCategory === 'all' ||
            section.title.toLowerCase().includes(selectedCategory.toLowerCase());
          return matchesSearch && matchesCategory;
        }),
      }))
      .filter((section) => section.resources.length > 0);
  }, [sections, searchQuery, selectedCategory]);

  const categories = ['all', ...sections.map((section) => section.title)];

  return (
    <div className="min-h-screen bg-gradient-to-b from-midnight to-midnight/95 text-snow pt-24">
      <motion.div
        className="container mx-auto px-6 py-12"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Add Navigation Buttons */}
        <motion.div variants={itemVariants} className="flex gap-4 mb-12">
          <Link
            to="/"
            className="inline-flex items-center px-4 py-2 rounded-xl bg-white/[0.03] hover:bg-white/[0.06] 
              border border-white/10 hover:border-white/20 transition-all duration-300 text-snow/70 
              hover:text-snow group"
          >
            <ArrowLeft className="w-5 h-5 mr-2 transition-transform group-hover:-translate-x-1" />
            Back to Home
          </Link>
          <Link
            to="/tools"
            className="inline-flex items-center px-4 py-2 rounded-xl bg-emerald-500/10 hover:bg-emerald-500/20 
              border border-emerald-500/20 hover:border-emerald-500/30 transition-all duration-300 
              text-emerald-400 hover:text-emerald-300 group"
          >
            <Wrench className="w-5 h-5 mr-2 transition-transform group-hover:rotate-12" />
            View Tools
          </Link>
        </motion.div>

        <motion.div variants={itemVariants} className="relative text-center mb-20">
          <div className="absolute inset-0 -z-10 blur-3xl bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-full" />
          <motion.div
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
            className="relative"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <Sparkles className="w-8 h-8 text-emerald-400" />
              <h1 className="text-5xl font-bold bg-gradient-to-r from-emerald-400 via-cyan-400 to-blue-500 bg-clip-text text-transparent">
                Learn & Grow
              </h1>
              <Sparkles className="w-8 h-8 text-blue-400" />
            </div>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
              Embark on your AI journey with our carefully curated learning paths
            </p>
          </motion.div>
        </motion.div>

        <motion.div variants={itemVariants} className="max-w-3xl mx-auto mb-12">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder="Search resources..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-white/[0.03] border-white/10 backdrop-blur-sm focus:border-emerald-500 focus:ring-emerald-500/20 text-snow placeholder-gray-400"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          <div className="flex gap-2 mt-4 flex-wrap">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-1 rounded-full text-sm transition-all ${
                  selectedCategory === category
                    ? 'bg-emerald-500 text-white'
                    : 'bg-white/[0.03] hover:bg-white/[0.06] text-gray-300'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </motion.div>

        <AnimatePresence mode="wait">
          {filteredSections.length > 0 ? (
            <div className="grid gap-16 relative">
              <div className="absolute inset-0 -z-10 bg-gradient-to-b from-emerald-500/5 to-blue-500/5 blur-3xl" />
              {filteredSections.map((section) => (
                <motion.section
                  key={section.title}
                  variants={itemVariants}
                  className="relative z-10"
                >
                  <div className="flex items-center gap-3 mb-8">
                    <div className="p-2 rounded-xl bg-emerald-500/10 backdrop-blur-sm">
                      {section.icon}
                    </div>
                    <h2 className="text-3xl font-semibold bg-gradient-to-r from-emerald-400 to-emerald-500 bg-clip-text text-transparent">
                      {section.title}
                    </h2>
                  </div>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {section.resources.map((resource) => (
                      <motion.div
                        key={resource.name}
                        whileHover={{ scale: 1.02, y: -5 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Card className="bg-white/[0.03] border-white/10 backdrop-blur-sm hover:bg-white/[0.06] transition-all duration-300 h-full">
                          <CardHeader>
                            <CardTitle className="text-emerald-400 text-xl">
                              {resource.name}
                            </CardTitle>
                            <CardDescription className="text-gray-400 text-base">
                              {resource.description}
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <a
                              href={resource.link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-2 text-sm text-emerald-500 hover:text-emerald-400 transition-colors duration-200 group"
                            >
                              Visit Resource
                              <span className="transform transition-transform group-hover:translate-x-1">
                                →
                              </span>
                            </a>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </motion.section>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              className="text-center text-gray-400 py-12"
            >
              <p className="text-xl">No resources found matching your search criteria</p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('all');
                }}
                className="mt-4 text-emerald-500 hover:text-emerald-400"
              >
                Clear filters
              </button>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default Education;
