import { CommandPalette } from '@/components/CommandPalette';
import { EnhancedErrorBoundary, setupGlobalErrorHandling } from '@/components/ErrorBoundary';
import LoadingScreen from '@/components/LoadingScreen';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';
import { usePerformanceMonitoring } from '@/hooks/use-performance';
import { useAppStore } from '@/stores/app-store';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { Suspense, useEffect } from 'react';
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';

// Lazy load pages for better performance
const Index = React.lazy(() => import('./pages/Index'));
const Tools = React.lazy(() => import('./pages/Tools'));
const Education = React.lazy(() => import('./pages/Education'));
const ModernizePricing = React.lazy(() => import('./pages/ModernizePricing'));
const AutomationPricing = React.lazy(() => import('./pages/AutomationPricing'));
const ToolsAdmin = React.lazy(() => import('./pages/ToolsAdmin'));
const AdminLayout = React.lazy(() => import('./layouts/AdminLayout'));
const ApplicationsAdmin = React.lazy(() => import('./pages/admin/applications'));
const AIDevelopmentPricing = React.lazy(() => import('./pages/AIDevelopmentPricing'));
const DataSciencePricing = React.lazy(() => import('./pages/DataSciencePricing'));
const BinanceWidget = React.lazy(() => import('./pages/BinanceWidget'));

// Ultra-modern QueryClient with advanced configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Smart retry logic
        if (error?.status === 404) return false;
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
      refetchOnReconnect: 'always',
    },
    mutations: {
      retry: 1,
    },
  },
});

// App component with ultra-modern architecture
const App: React.FC = () => {
  const commandPaletteOpen = useAppStore((state) => state.ui.commandPaletteOpen);
  const toggleCommandPalette = useAppStore((state) => state.toggleCommandPalette);
  const { measureRender } = usePerformanceMonitoring();

  // Initialize global error handling and performance monitoring
  useEffect(() => {
    setupGlobalErrorHandling();

    // Measure app initialization time
    const endMeasurement = measureRender('App');

    // Performance optimization: preload critical resources
    const preloadCriticalResources = async () => {
      // Preload fonts
      const fontPromises = [new FontFace('Inter', 'url(/fonts/inter.woff2)').load()];

      try {
        await Promise.all(fontPromises);
      } catch (error) {
        console.warn('Failed to preload fonts:', error);
      }
    };

    preloadCriticalResources();

    return endMeasurement;
  }, [measureRender]);

  return (
    <EnhancedErrorBoundary name="App">
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Router>
            <Suspense fallback={<LoadingScreen />}>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="tools">
                  <Route index element={<Tools />} />
                  <Route path="admin" element={<ToolsAdmin />} />
                </Route>
                <Route path="/education" element={<Education />} />
                <Route path="/services/modernize" element={<ModernizePricing />} />
                <Route path="/services/automation" element={<AutomationPricing />} />
                <Route path="/services/ai-development" element={<AIDevelopmentPricing />} />
                <Route path="/services/data-science" element={<DataSciencePricing />} />
                <Route
                  path="/admin"
                  element={
                    <Suspense fallback={<LoadingScreen />}>
                      <AdminLayout>
                        <ApplicationsAdmin />
                      </AdminLayout>
                    </Suspense>
                  }
                />
                <Route path="/binance-widget" element={<BinanceWidget />} />
              </Routes>
            </Suspense>

            {/* Global Command Palette */}
            <CommandPalette open={commandPaletteOpen} onOpenChange={toggleCommandPalette} />

            {/* Toast Notifications */}
            <Toaster />
            <Sonner />
          </Router>
        </TooltipProvider>
      </QueryClientProvider>
    </EnhancedErrorBoundary>
  );
};

export default App;
