import { Github, <PERSON>edin, Mail, Twitter } from 'lucide-react';
import React from 'react';

const Footer = () => {
  return (
    <footer className="bg-midnight/90 py-12 px-4">
      <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <h4 className="text-lg font-semibold mb-4">Connect</h4>
          <div className="flex space-x-4">
            <a
              href="https://github.com/Sainpse"
              target="_blank"
              rel="noopener noreferrer"
              className="text-snow/80 hover:text-jungle transition-colors"
            >
              <Github className="w-6 h-6" />
            </a>
            <a
              href="https://x.com/Sainpse"
              target="_blank"
              rel="noopener noreferrer"
              className="text-snow/80 hover:text-jungle transition-colors"
            >
              <Twitter className="w-6 h-6" />
            </a>
            <a
              href="https://www.linkedin.com/company/sainpse"
              target="_blank"
              rel="noopener noreferrer"
              className="text-snow/80 hover:text-jungle transition-colors"
            >
              <Linkedin className="w-6 h-6" />
            </a>
            <a
              href="mailto:<EMAIL>"
              className="text-snow/80 hover:text-jungle transition-colors"
            >
              <Mail className="w-6 h-6" />
            </a>
          </div>
        </div>

        <div>
          <h4 className="text-lg font-semibold mb-4">Contact</h4>
          <div className="flex space-x-4">
            <p>Whatsapp: +27 (74) 944-2626</p>
          </div>
        </div>
      </div>
      <div className="mt-8 pt-8 border-t border-jungle/20 text-center text-snow/60">
        <p>&copy; 2024 Sainpse Institute. All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;
