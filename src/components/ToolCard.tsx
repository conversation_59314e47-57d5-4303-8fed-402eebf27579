import { motion } from 'framer-motion';
import { Share2, Star } from 'lucide-react';
import type { Tool } from '../types/tools';

interface ToolCardProps {
  tool: Tool;
  onRate: (score: number) => void;
}

const ToolCard: React.FC<ToolCardProps> = ({ tool, onRate }) => {
  return (
    <motion.a
      layout
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      href={tool.url}
      target="_blank"
      rel="noopener noreferrer"
      className="block p-5 bg-midnight/40 rounded-xl border border-white/5 hover:border-emerald-500/30
        transition-all duration-300 hover:bg-emerald-500/5 group relative backdrop-blur-sm"
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-medium text-emerald-500 group-hover:text-emerald-400">
          {tool.name}
          {tool.featured && (
            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-500/20 text-emerald-400">
              Featured
            </span>
          )}
        </h3>
        <button
          onClick={(e) => {
            e.preventDefault();
            navigator.share?.({
              title: tool.name,
              text: tool.description,
              url: tool.url,
            });
          }}
          className="p-1.5 rounded-lg hover:bg-white/10 text-snow/50 hover:text-snow/80"
        >
          <Share2 className="w-4 h-4" />
        </button>
      </div>
      <p className="text-sm text-snow/70 mt-2 group-hover:text-snow/80">{tool.description}</p>

      {tool.rating && (
        <div className="flex items-center mt-3 text-sm">
          <div className="flex items-center text-amber-400">
            <Star className="w-4 h-4 fill-current" />
            <span className="ml-1">{tool.rating.score}</span>
          </div>
          <span className="text-snow/50 ml-2">({tool.rating.votes.toLocaleString()} votes)</span>
        </div>
      )}

      {tool.tags && (
        <div className="flex flex-wrap gap-2 mt-3">
          {tool.tags.map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-white/5 text-snow/70"
            >
              {tag}
            </span>
          ))}
        </div>
      )}
    </motion.a>
  );
};

export default ToolCard;
