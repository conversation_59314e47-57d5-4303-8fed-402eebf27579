import { motion } from 'framer-motion';
import { <PERSON>R<PERSON>, Bot, Code, Database, Network } from 'lucide-react';
import React, { useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';

const services = [
  {
    icon: Bot,
    title: 'Modernize',
    description:
      'Building intelligent websites and integrating AI-driven customer support agents to enhance user experience and engagement.',
    link: '/services/modernize',
  },
  {
    icon: Network,
    title: 'Automation',
    description:
      'Automating repetitive tasks and streamlining workflows with to enhance efficiency and scalability',
    link: '/services/automation',
  },
  {
    icon: Code,
    title: 'AI Development',
    description:
      'Advancing Large Language Models (LLMs) and integrating cutting-edge AI into real-world applications for transformative impact.',
    link: '/services/ai-development', // This is correct, just ensure the route exists
  },
  {
    icon: Database,
    title: 'Data Science',
    description: 'Transforming raw data into actionable intelligence.',
    link: '/services/data-science',
  },
];

const Services = () => {
  return (
    <section className="py-20 px-4 relative overflow-hidden">
      {/* Background Effect */}
      <div className="absolute inset-0 bg-gradient-to-b from-midnight/0 via-emerald-500/5 to-midnight/0" />

      <motion.h2
        initial={{ y: 20, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        viewport={{ once: true }}
        className="text-3xl md:text-5xl font-bold text-center mb-16 relative"
      >
        Our Services
      </motion.h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto relative">
        {services.map((service, index) => {
          const Icon = service.icon;
          return (
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              key={service.title}
              className="group backdrop-blur-sm bg-gradient-to-br from-midnight/80 to-midnight/40 p-8 rounded-xl border border-emerald-500/20 hover:border-emerald-500 transition-all duration-500 transform hover:-translate-y-2 hover:shadow-2xl hover:shadow-emerald-500/10 flex flex-col"
            >
              <div className="relative">
                <Icon className="w-12 h-12 text-emerald-500 mb-6 transition-all duration-300 group-hover:scale-110 group-hover:filter drop-shadow-glow" />
                <motion.div
                  className="absolute inset-0 bg-emerald-500/20 blur-xl rounded-full"
                  initial={{ scale: 0 }}
                  whileHover={{ scale: 1.5 }}
                  transition={{ duration: 0.3 }}
                />
              </div>

              <motion.h3
                className="text-2xl font-semibold mb-3 bg-gradient-to-r from-snow to-emerald-300 bg-clip-text text-transparent"
                initial={{ opacity: 0.8 }}
                whileHover={{ opacity: 1 }}
              >
                {service.title}
              </motion.h3>

              <p className="text-snow/70 mb-6 flex-grow leading-relaxed">{service.description}</p>

              <Link
                to={service.link}
                className="inline-flex items-center text-emerald-500 hover:text-emerald-400 transition-all duration-300 mt-auto group/link"
              >
                <span className="relative">
                  Learn more
                  <motion.span
                    className="absolute bottom-0 left-0 w-0 h-0.5 bg-emerald-500/50 group-hover/link:w-full transition-all duration-300"
                    initial={{ width: '0%' }}
                    whileHover={{ width: '100%' }}
                  />
                </span>
                <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover/link:translate-x-2" />
              </Link>
            </motion.div>
          );
        })}
      </div>
    </section>
  );
};

export default Services;
