import { motion } from 'framer-motion';
import { CheckCircle2, HelpCircle } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import { TooltipContent, TooltipProvider, TooltipRoot, TooltipTrigger } from './ui/custom-tooltip';

interface PricingCardProps {
  title: string;
  price: string;
  description: string;
  features: string[];
  recommendation: string;
  isPopular?: boolean;
  annualPrice?: string;
  savePercentage?: number;
  featureDetails?: { [key: string]: string };
}

const PricingCard: React.FC<PricingCardProps> = ({
  title,
  price,
  description,
  features,
  recommendation,
  isPopular,
  annualPrice,
  savePercentage,
  featureDetails = {},
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isAnnual, setIsAnnual] = useState(false);

  return (
    <motion.div
      initial={{ scale: 1 }}
      whileHover={{ scale: 1.02 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className={`flex flex-col h-[700px] rounded-xl p-8 relative backdrop-blur-md
        ${
          isPopular
            ? 'bg-white/10 border-2 border-emerald-500'
            : 'bg-white/5 hover:bg-white/10 border border-white/20'
        }`}
    >
      {isPopular && (
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="absolute -top-4 left-1/2 transform -translate-x-1/2"
        >
          <span className="bg-emerald-500 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">
            Most Popular
          </span>
        </motion.div>
      )}

      <div className="flex-none">
        <h3 className="text-2xl font-bold mb-2 text-snow">{title}</h3>
        <div className="mb-4 relative">
          <div className="flex items-baseline gap-2">
            <span className="text-4xl font-bold text-emerald-500">
              {isAnnual ? annualPrice || price : price}
            </span>
            <span className="text-snow/60">/month</span>
          </div>

          {annualPrice && (
            <div className="mt-2">
              <label className="flex items-center gap-2 text-sm text-snow/80">
                <input
                  type="checkbox"
                  checked={isAnnual}
                  onChange={() => setIsAnnual(!isAnnual)}
                  className="rounded border-white/20 bg-white/5"
                />
                Bill annually
                {savePercentage && (
                  <span className="text-emerald-500 text-xs font-bold">
                    (Save {savePercentage}%)
                  </span>
                )}
              </label>
            </div>
          )}
        </div>
        <p className="text-snow/80 mb-6 h-[60px]">{description}</p>
      </div>

      <div className="flex-grow">
        <ul className="space-y-3 mb-6">
          {features.map((feature, index) => (
            <motion.li
              key={index}
              initial={{ x: -10, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start group"
            >
              <CheckCircle2 className="w-5 h-5 text-emerald-500 mr-2 mt-1 flex-shrink-0" />
              <span className="text-sm text-snow/80 flex-1">{feature}</span>
              {featureDetails[feature] && (
                <TooltipProvider>
                  <TooltipRoot delayDuration={200}>
                    <TooltipTrigger asChild>
                      <button
                        className="ml-2 rounded-full p-1.5 bg-emerald-500/10 hover:bg-emerald-500/20 
                                 transition-colors group-hover:opacity-100 opacity-75 z-50"
                      >
                        <HelpCircle className="w-4 h-4 text-emerald-500" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-[250px] text-snow/90 leading-relaxed">
                        {featureDetails[feature]}
                      </p>
                    </TooltipContent>
                  </TooltipRoot>
                </TooltipProvider>
              )}
            </motion.li>
          ))}
        </ul>
      </div>

      <div className="flex-none mt-6">
        <p className="text-sm text-snow/60 italic mb-4 h-[40px]">{recommendation}</p>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className={`w-full py-3 rounded-lg font-semibold transition-colors duration-200
          ${
            isPopular
              ? 'bg-emerald-500 hover:bg-emerald-600 text-white'
              : 'bg-white/10 hover:bg-white/20 text-snow'
          }`}
        >
          Get Started
        </motion.button>
      </div>
    </motion.div>
  );
};

export default PricingCard;
