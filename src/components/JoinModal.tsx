import { AnimatePresence, motion } from 'framer-motion';
import { Check, Loader2, X } from 'lucide-react';
import React from 'react';
import { submitJoinApplication } from '../lib/supabase';

interface JoinModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const JoinModal = ({ isOpen, onClose }: JoinModalProps) => {
  const [step, setStep] = React.useState(1);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitSuccess, setSubmitSuccess] = React.useState(false);
  const [submitError, setSubmitError] = React.useState<string | null>(null);
  const [formData, setFormData] = React.useState({
    fullName: '',
    email: '',
    phone: '',
    website: '',
    jobTitle: '',
    company: '',
    experience: '',
    skills: [],
    serviceInterest: '',
    engagementType: '',
    motivation: '',
    goals: '',
    aiExperience: '',
    projects: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Only process submission if we're on the last step
    if (step !== 4) {
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      await submitJoinApplication({
        full_name: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        website: formData.website,
        job_title: formData.jobTitle,
        company: formData.company,
        experience: formData.experience,
        skills: [],
        service_interest: formData.serviceInterest,
        engagement_type: formData.engagementType,
        motivation: formData.motivation,
        goals: formData.goals,
        ai_experience: formData.aiExperience,
        projects: formData.projects,
      });
      setSubmitSuccess(true);
      // Close modal after 2 seconds
      setTimeout(() => {
        onClose();
        setSubmitSuccess(false);
        setFormData({
          // Reset form
          fullName: '',
          email: '',
          phone: '',
          website: '',
          jobTitle: '',
          company: '',
          experience: '',
          skills: [],
          serviceInterest: '',
          engagementType: '',
          motivation: '',
          goals: '',
          aiExperience: '',
          projects: '',
        });
      }, 2000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'Failed to submit application');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-4">
            <h3 className="text-xl font-semibold mb-6">Personal & Contact Information</h3>
            <div>
              <label className="block text-sm font-medium mb-2">Full Name</label>
              <input
                type="text"
                name="fullName"
                value={formData.fullName}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Email Address</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Phone Number</label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">LinkedIn or Personal Website</label>
              <input
                type="url"
                name="website"
                value={formData.website}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
              />
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-4">
            <h3 className="text-xl font-semibold mb-6">Professional Background</h3>
            <div>
              <label className="block text-sm font-medium mb-2">Current Job Title</label>
              <input
                type="text"
                name="jobTitle"
                value={formData.jobTitle}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Company</label>
              <input
                type="text"
                name="company"
                value={formData.company}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Years of Experience</label>
              <input
                type="text"
                name="experience"
                value={formData.experience}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
              />
            </div>
          </div>
        );
      case 3:
        return (
          <div className="space-y-4">
            <h3 className="text-xl font-semibold mb-6">Area of Interest</h3>
            <div>
              <label className="block text-sm font-medium mb-2">Service Interest</label>
              <select
                name="serviceInterest"
                value={formData.serviceInterest}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500 appearance-none"
              >
                <option value="">Select a service</option>
                <option value="modernization">Modernization</option>
                <option value="automation">Automation</option>
                <option value="ai-development">AI Development</option>
                <option value="data-science">Data Science</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Engagement Type</label>
              <select
                name="engagementType"
                value={formData.engagementType}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500 appearance-none"
              >
                <option value="">Select type</option>
                <option value="full-time">Full-time</option>
                <option value="part-time">Part-time</option>
                <option value="freelance">Freelance</option>
                <option value="collaboration">Collaboration</option>
              </select>
            </div>
          </div>
        );
      case 4:
        return (
          <div className="space-y-4">
            <h3 className="text-xl font-semibold mb-6">Motivation & Technical Experience</h3>
            <div>
              <label className="block text-sm font-medium mb-2">
                Why are you interested in joining?
              </label>
              <textarea
                name="motivation"
                value={formData.motivation}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
                rows={3}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">AI/LLM Experience</label>
              <textarea
                name="aiExperience"
                value={formData.aiExperience}
                onChange={updateFormData}
                className="w-full px-4 py-2 rounded-lg bg-midnight-700 text-black border border-white/10 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
                rows={3}
              />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const renderContent = () => {
    if (submitSuccess) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="bg-emerald-500/20 p-4 rounded-full mb-4">
            <Check className="w-8 h-8 text-emerald-500" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Application Submitted!</h3>
          <p className="text-snow/70 text-center">
            Thank you for your interest. We'll review your application and get back to you soon.
          </p>
        </div>
      );
    }

    return (
      <>
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2">Join Synapsetron</h2>
          <div className="flex gap-2">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className={`h-1 flex-1 rounded-full ${
                  i <= step ? 'bg-emerald-500' : 'bg-white/10'
                }`}
              />
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          {submitError && (
            <div className="mb-4 p-4 bg-red-500/20 border border-red-500/50 rounded-lg text-red-500">
              {submitError}
            </div>
          )}

          {renderStep()}

          <div className="flex justify-between mt-8">
            {step > 1 && (
              <button
                type="button"
                disabled={isSubmitting}
                onClick={(e) => {
                  e.preventDefault();
                  setStep((s) => s - 1);
                }}
                className="px-6 py-2 rounded-lg border border-white/10 hover:bg-white/5 transition-colors disabled:opacity-50"
              >
                Previous
              </button>
            )}
            {step < 4 ? (
              <button
                type="button"
                disabled={isSubmitting}
                onClick={(e) => {
                  e.preventDefault();
                  setStep((s) => s + 1);
                }}
                className="px-6 py-2 rounded-lg bg-emerald-500 hover:bg-emerald-600 transition-colors ml-auto disabled:opacity-50"
              >
                Next
              </button>
            ) : (
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 rounded-lg bg-emerald-500 hover:bg-emerald-600 transition-colors ml-auto disabled:opacity-50 flex items-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit'
                )}
              </button>
            )}
          </div>
        </form>
      </>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-midnight relative w-full max-w-2xl rounded-2xl p-8 border border-white/10"
          >
            <button
              onClick={onClose}
              disabled={isSubmitting}
              className="absolute right-4 top-4 text-snow/50 hover:text-snow transition-colors disabled:opacity-50"
            >
              <X className="w-6 h-6" />
            </button>

            {renderContent()}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default JoinModal;
