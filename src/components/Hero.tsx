import { motion, useReducedMotion } from 'framer-motion';
import React, { Suspense } from 'react';
import { Link } from 'react-router-dom';

const NetworkAnimation = React.lazy(() => import('./NetworkAnimation'));

const Hero = () => {
  const shouldReduceMotion = useReducedMotion();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1],
      },
    },
  };

  const floatingAnimation = {
    hidden: { y: 0 },
    visible: {
      y: [-10, 10, -10],
      transition: {
        duration: 6,
        repeat: Number.POSITIVE_INFINITY,
        ease: 'easeInOut',
      },
    },
  };

  const waveAnimation = {
    hidden: { opacity: 0 },
    visible: (i) => ({
      opacity: 1,
      transition: {
        delay: i * (shouldReduceMotion ? 0.05 : 0.1),
        yoyo: shouldReduceMotion ? false : Number.POSITIVE_INFINITY,
        duration: shouldReduceMotion ? 0.5 : 1.5,
      },
    }),
  };

  return (
    <div className="relative min-h-[90vh] md:min-h-screen flex items-center justify-center overflow-hidden">
      <div className="absolute inset-0">
        <Suspense fallback={<div className="w-full h-full bg-midnight" />}>
          <NetworkAnimation />
        </Suspense>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/40 to-black" />
      </div>

      <motion.div
        className="text-center z-10 px-4 sm:px-6 lg:px-8 relative max-w-5xl mx-auto"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <motion.h2 className="text-lg md:text-xl font-medium mb-4" variants={itemVariants}>
          {'Welcome to the'.split('').map((letter, i) => (
            <motion.span
              key={i}
              custom={i}
              variants={waveAnimation}
              style={{ display: 'inline-block' }}
            >
              {letter === ' ' ? '\u00A0' : letter}
            </motion.span>
          ))}
        </motion.h2>

        <motion.h1
          className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6"
          variants={itemVariants}
        >
          Sainpse Institute of{' '}
          <span className="text-emerald-500 block mt-2">Augmented Intelligence</span>
        </motion.h1>

        <motion.p
          className="text-xl md:text-2xl mb-8 text-snow/90 max-w-2xl mx-auto"
          variants={itemVariants}
        >
          Unlocking Extraordinary Possibilities Through the Convergence of Human and Artificial
          Intelligence
        </motion.p>

        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4"
        >
          <Link to="/education">
            <motion.button
              className="w-full sm:w-auto bg-emerald-500 text-midnight px-6 py-3 rounded-full text-lg font-semibold transition-all duration-300"
              whileHover={{
                scale: 1.05,
                boxShadow: '0 0 30px rgba(16, 185, 129, 0.6)',
              }}
              whileTap={{ scale: 0.98 }}
              animate={{
                boxShadow: ['0 0 0 0 rgba(16, 185, 129, 0.4)', '0 0 0 10px rgba(16, 185, 129, 0)'],
              }}
              transition={{
                duration: 1.5,
                repeat: Number.POSITIVE_INFINITY,
                ease: 'easeInOut',
              }}
              aria-label="Explore our educational resources"
            >
              Open Education
            </motion.button>
          </Link>

          <Link to="/tools">
            <motion.button
              className="bg-transparent border-2 border-emerald-500 text-emerald-500 px-6 py-3 rounded-full text-lg font-semibold transition-all duration-300"
              whileHover={{
                scale: 1.05,
                boxShadow: '0 0 30px rgba(16, 185, 129, 0.3)',
              }}
              whileTap={{ scale: 0.98 }}
            >
              Free Tools
            </motion.button>
          </Link>
        </motion.div>

        <motion.div
          className="mt-12 grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8"
          variants={itemVariants}
        >
          {[
            { number: '4+', label: 'Service Offers' },
            { number: 'Coming Soon', label: 'In-House' },
            { number: 'Empowerment', label: 'Initiative' },
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="text-center"
              variants={floatingAnimation}
              animate="visible"
              custom={index * 0.2}
            >
              <motion.p className="text-3xl font-bold text-emerald-500" whileHover={{ scale: 1.1 }}>
                {stat.number}
              </motion.p>
              <p className="text-sm text-snow/70">{stat.label}</p>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Hero;
