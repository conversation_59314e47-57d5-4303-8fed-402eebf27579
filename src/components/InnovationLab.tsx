import React, { useState } from 'react';
import JoinModal from './JoinModal';

const InnovationLab = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <section className="relative py-32 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-midnight to-midnight/50 z-10" />
      <div
        className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80')] bg-cover bg-center"
        style={{ transform: 'translateZ(-10px)' }}
      />
      <div className="relative z-20 max-w-4xl mx-auto text-center px-4">
        <h2 className="text-4xl md:text-5xl font-bold mb-6">Shape the Future</h2>
        <p className="text-xl mb-8">
          Join us and help us build a future where Collaboration, Emergence and Empowerment are the
          driving forces of progress.
        </p>
        <button
          onClick={() => setIsModalOpen(true)}
          className="bg-emerald-500 text-midnight px-8 py-3 rounded-full text-lg font-semibold transition-all duration-300 hover:glow-effect"
        >
          Join
        </button>
      </div>

      <JoinModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </section>
  );
};

export default InnovationLab;
