import { motion } from 'framer-motion';
import React from 'react';
import { PuffLoader } from 'react-spinners';

const LoadingScreen = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-midnight to-midnight/95 text-snow flex items-center justify-center">
      <div className="fixed inset-0 bg-grid-white/[0.02] bg-[size:60px_60px] pointer-events-none" />
      <div className="text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center gap-8"
        >
          <PuffLoader color="#10b981" size={60} />
          <div className="space-y-3">
            <h2 className="text-2xl font-semibold text-snow">Loading Tools</h2>
            <p className="text-snow/70">Preparing your development arsenal...</p>
          </div>

          <motion.div
            className="mt-8 flex gap-3"
            animate={{
              scale: [1, 1.05, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              ease: 'easeInOut',
            }}
          >
            {[...Array(3)].map((_, i) => (
              <div key={i} className="w-3 h-3 rounded-full bg-emerald-500/50" />
            ))}
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default LoadingScreen;
