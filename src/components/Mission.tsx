import { motion } from 'framer-motion';
import React from 'react';

const Mission = () => {
  return (
    <motion.section
      id="mission"
      className="py-20 px-4 relative overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      {/* Animated background decoration */}
      <motion.div
        className="absolute inset-0 opacity-30"
        animate={{
          background: [
            'radial-gradient(circle at 20% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)',
            'radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)',
            'radial-gradient(circle at 20% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)',
          ],
        }}
        transition={{ duration: 10, repeat: Number.POSITIVE_INFINITY }}
      />

      <div className="max-w-4xl mx-auto text-center relative">
        <motion.h2
          className="text-3xl md:text-4xl font-bold mb-8"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          Our Mission
        </motion.h2>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.p
            className="text-xl leading-relaxed mb-6"
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            The Sainpse Institute of augmented intelligence where human ingenuity and artificial
            intelligence converge to unlock extraordinary possibilities.{' '}
            <motion.span
              className="text-emerald-500 inline-block"
              whileHover={{ scale: 1.05, color: '#34d399' }}
              transition={{ duration: 0.2 }}
            >
              Inspired by the brain's synapses, the essence of connection and emergence
            </motion.span>
            , we believe intelligent systems are not limited to technology but extend to society and
            organizations working in harmony.
          </motion.p>

          <motion.p
            className="text-xl leading-relaxed mb-6"
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            Our mission is to empower individuals, business and communities to collaborate in ways
            that
            <motion.span
              className="mx-1 text-emerald-400 font-medium"
              whileHover={{ scale: 1.05, color: '#34d399' }}
              transition={{ duration: 0.2 }}
            >
              amplify intelligence, foster innovation, ease connection,
            </motion.span>
            and drive transformative progress. By leveraging cutting-edge Augmented/AI tools and
            embracing the collective potential of networks, we aim to advance knowledge, spark
            creativity, and shape a future where intelligence is truly emergent at all levels.
          </motion.p>

          <motion.p
            className="text-xl leading-relaxed"
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            Sainpse is more than a philosophy—it's a movement to inspire systems, both human and
            digital, to achieve more together than they ever could alone.
          </motion.p>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default Mission;
