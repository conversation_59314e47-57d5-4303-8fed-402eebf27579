import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useAppActions, useAppStore } from '@/stores/app-store';
import { Command } from 'cmdk';
import { AnimatePresence, motion } from 'framer-motion';
import {
  Bookmark,
  Copy,
  Download,
  ExternalLink,
  HelpCircle,
  History,
  Keyboard,
  Monitor,
  Moon,
  Palette,
  Search,
  Settings,
  Share,
  Sparkles,
  Sun,
  User,
  Zap,
} from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { useNavigate } from 'react-router-dom';

interface CommandItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: React.ComponentType<{ className?: string }>;
  action: () => void | Promise<void>;
  keywords: string[];
  category: 'navigation' | 'actions' | 'settings' | 'help' | 'ai';
  shortcut?: string[];
  priority?: number;
  aiRelevance?: number;
}

interface CommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CommandPalette: React.FC<CommandPaletteProps> = ({ open, onOpenChange }) => {
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const navigate = useNavigate();
  const { preferences } = useAppStore();
  const { updatePreferences, addNotification, trackEvent } = useAppActions();

  // Subscribe to analytics events to properly track changes
  const analyticsEvents = useAppStore((state) => state.analytics?.events || []);

  // AI-powered command suggestions based on user behavior
  const getAIRelevanceScore = useCallback(
    (command: CommandItem, userHistory: string[]): number => {
      let score = command.priority || 0;

      // Boost score based on recent usage
      const recentUsage = userHistory.filter((h) => h === command.id).length;
      score += recentUsage * 10;

      // Boost score based on time of day patterns
      const hour = new Date().getHours();
      if (command.category === 'settings' && (hour < 9 || hour > 17)) {
        score += 5; // People tend to adjust settings outside work hours
      }

      // Boost score based on current theme
      if (command.id.includes('theme') && preferences.theme === 'system') {
        score += 3;
      }

      return score;
    },
    [preferences.theme]
  );

  // Define all available commands
  const commands: CommandItem[] = useMemo(
    () => [
      // Navigation Commands
      {
        id: 'nav-home',
        title: 'Go to Home',
        subtitle: 'Navigate to the main page',
        icon: Zap,
        action: () => navigate('/'),
        keywords: ['home', 'main', 'index', 'start'],
        category: 'navigation',
        shortcut: ['ctrl', 'h'],
        priority: 10,
      },
      {
        id: 'nav-tools',
        title: 'Go to Tools',
        subtitle: 'Explore our AI tools',
        icon: Settings,
        action: () => navigate('/tools'),
        keywords: ['tools', 'ai', 'utilities'],
        category: 'navigation',
        shortcut: ['ctrl', 't'],
        priority: 8,
      },
      {
        id: 'nav-education',
        title: 'Go to Education',
        subtitle: 'Learn with our courses',
        icon: Sparkles,
        action: () => navigate('/education'),
        keywords: ['education', 'learn', 'courses', 'training'],
        category: 'navigation',
        priority: 7,
      },

      // Theme Commands
      {
        id: 'theme-light',
        title: 'Switch to Light Theme',
        subtitle: 'Use light color scheme',
        icon: Sun,
        action: () => updatePreferences({ theme: 'light' }),
        keywords: ['light', 'theme', 'bright', 'day'],
        category: 'settings',
        priority: 5,
      },
      {
        id: 'theme-dark',
        title: 'Switch to Dark Theme',
        subtitle: 'Use dark color scheme',
        icon: Moon,
        action: () => updatePreferences({ theme: 'dark' }),
        keywords: ['dark', 'theme', 'night', 'black'],
        category: 'settings',
        priority: 5,
      },
      {
        id: 'theme-system',
        title: 'Use System Theme',
        subtitle: 'Follow system preference',
        icon: Monitor,
        action: () => updatePreferences({ theme: 'system' }),
        keywords: ['system', 'theme', 'auto', 'automatic'],
        category: 'settings',
        priority: 6,
      },

      // AI Personality Commands
      {
        id: 'ai-professional',
        title: 'Professional AI Mode',
        subtitle: 'Formal and business-focused',
        icon: User,
        action: () => updatePreferences({ aiPersonality: 'professional' }),
        keywords: ['professional', 'business', 'formal', 'work'],
        category: 'ai',
        priority: 4,
      },
      {
        id: 'ai-creative',
        title: 'Creative AI Mode',
        subtitle: 'Innovative and artistic',
        icon: Palette,
        action: () => updatePreferences({ aiPersonality: 'creative' }),
        keywords: ['creative', 'artistic', 'innovative', 'fun'],
        category: 'ai',
        priority: 4,
      },
      {
        id: 'ai-quantum',
        title: 'Quantum AI Mode',
        subtitle: 'Advanced and experimental',
        icon: Sparkles,
        action: () => updatePreferences({ aiPersonality: 'quantum' }),
        keywords: ['quantum', 'advanced', 'experimental', 'future'],
        category: 'ai',
        priority: 4,
      },

      // Action Commands
      {
        id: 'toggle-animations',
        title: preferences.animationsEnabled ? 'Disable Animations' : 'Enable Animations',
        subtitle: 'Toggle motion effects',
        icon: Zap,
        action: () => updatePreferences({ animationsEnabled: !preferences.animationsEnabled }),
        keywords: ['animations', 'motion', 'effects', 'performance'],
        category: 'settings',
        priority: 3,
      },
      {
        id: 'toggle-sound',
        title: preferences.soundEnabled ? 'Disable Sound' : 'Enable Sound',
        subtitle: 'Toggle audio feedback',
        icon: preferences.soundEnabled ? Moon : Sun,
        action: () => updatePreferences({ soundEnabled: !preferences.soundEnabled }),
        keywords: ['sound', 'audio', 'feedback', 'mute'],
        category: 'settings',
        priority: 3,
      },
      {
        id: 'copy-url',
        title: 'Copy Current URL',
        subtitle: 'Copy page URL to clipboard',
        icon: Copy,
        action: async () => {
          await navigator.clipboard.writeText(window.location.href);
          addNotification({ type: 'success', message: 'URL copied to clipboard' });
        },
        keywords: ['copy', 'url', 'link', 'clipboard'],
        category: 'actions',
        priority: 2,
      },
      {
        id: 'share-page',
        title: 'Share Current Page',
        subtitle: 'Share via Web Share API',
        icon: Share,
        action: async () => {
          if (navigator.share) {
            await navigator.share({
              title: document.title,
              url: window.location.href,
            });
          } else {
            await navigator.clipboard.writeText(window.location.href);
            addNotification({ type: 'success', message: 'URL copied to clipboard' });
          }
        },
        keywords: ['share', 'send', 'social'],
        category: 'actions',
        priority: 2,
      },

      // Help Commands
      {
        id: 'help-shortcuts',
        title: 'View Keyboard Shortcuts',
        subtitle: 'See all available shortcuts',
        icon: Keyboard,
        action: () =>
          addNotification({
            type: 'info',
            message: 'Ctrl+K: Command Palette, Ctrl+H: Home, Ctrl+T: Tools',
          }),
        keywords: ['help', 'shortcuts', 'keyboard', 'hotkeys'],
        category: 'help',
        priority: 1,
      },
      {
        id: 'help-about',
        title: 'About Sainpse',
        subtitle: 'Learn about our mission',
        icon: HelpCircle,
        action: () => navigate('/#mission'),
        keywords: ['about', 'mission', 'company', 'info'],
        category: 'help',
        priority: 1,
      },
    ],
    [navigate, preferences, updatePreferences, addNotification]
  );

  // Memoized user history using subscribed analytics events
  const userHistory = useMemo(() => {
    return analyticsEvents
      .filter((e) => e.type === 'command_executed')
      .map((e) => e.data.commandId as string)
      .slice(-50); // Last 50 commands
  }, [analyticsEvents]);

  // Filter and sort commands based on search and AI relevance
  const filteredCommands = useMemo(() => {
    let filtered = commands;

    // Filter by category if selected
    if (selectedCategory) {
      filtered = filtered.filter((cmd) => cmd.category === selectedCategory);
    }

    // Filter by search query
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(
        (cmd) =>
          cmd.title.toLowerCase().includes(searchLower) ||
          cmd.subtitle?.toLowerCase().includes(searchLower) ||
          cmd.keywords.some((keyword) => keyword.toLowerCase().includes(searchLower))
      );
    }

    // Sort by AI relevance and priority
    return filtered.sort((a, b) => {
      const aScore = getAIRelevanceScore(a, userHistory);
      const bScore = getAIRelevanceScore(b, userHistory);
      return bScore - aScore;
    });
  }, [commands, search, selectedCategory, getAIRelevanceScore, userHistory]);

  // Group commands by category
  const groupedCommands = useMemo(() => {
    const groups: Record<string, CommandItem[]> = {};
    filteredCommands.forEach((cmd) => {
      if (!groups[cmd.category]) {
        groups[cmd.category] = [];
      }
      groups[cmd.category].push(cmd);
    });
    return groups;
  }, [filteredCommands]);

  // Execute command and track usage
  const executeCommand = useCallback(
    (command: CommandItem) => {
      trackEvent('command_executed', {
        commandId: command.id,
        commandTitle: command.title,
        category: command.category,
        searchQuery: search,
      });

      command.action();
      onOpenChange(false);
      setSearch('');
    },
    [trackEvent, search, onOpenChange]
  );

  // Keyboard shortcuts
  useHotkeys(
    'ctrl+k, cmd+k',
    (e) => {
      e.preventDefault();
      onOpenChange(!open);
    },
    { enableOnFormTags: true }
  );

  useHotkeys(
    'escape',
    () => {
      if (open) {
        onOpenChange(false);
      }
    },
    { enableOnFormTags: true, enabled: open }
  );

  // Clear search when opening
  useEffect(() => {
    if (open) {
      setSearch('');
      setSelectedCategory(null);
    }
  }, [open]);

  const categories = [
    { id: 'navigation', label: 'Navigation', icon: Zap },
    { id: 'actions', label: 'Actions', icon: Settings },
    { id: 'settings', label: 'Settings', icon: User },
    { id: 'ai', label: 'AI', icon: Sparkles },
    { id: 'help', label: 'Help', icon: HelpCircle },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0 max-w-2xl">
        <Command className="rounded-lg border shadow-md">
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Command.Input
              placeholder="Type a command or search..."
              value={search}
              onValueChange={setSearch}
              className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>

          {/* Category Filter */}
          <div className="flex gap-1 p-2 border-b">
            <button
              onClick={() => setSelectedCategory(null)}
              className={`px-2 py-1 text-xs rounded-md transition-colors ${
                !selectedCategory ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
              }`}
            >
              All
            </button>
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-2 py-1 text-xs rounded-md transition-colors flex items-center gap-1 ${
                  selectedCategory === category.id
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted'
                }`}
              >
                <category.icon className="w-3 h-3" />
                {category.label}
              </button>
            ))}
          </div>

          <Command.List className="max-h-[400px] overflow-y-auto">
            <Command.Empty>
              <div className="py-6 text-center text-sm text-muted-foreground">
                No commands found.
              </div>
            </Command.Empty>

            {Object.entries(groupedCommands).map(([category, commands]) => (
              <Command.Group
                key={category}
                heading={category.charAt(0).toUpperCase() + category.slice(1)}
              >
                {commands.map((command) => (
                  <Command.Item
                    key={command.id}
                    value={command.id}
                    onSelect={() => executeCommand(command)}
                    className="flex items-center gap-3 px-3 py-2 cursor-pointer hover:bg-muted rounded-sm"
                  >
                    <command.icon className="w-4 h-4 text-muted-foreground" />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium">{command.title}</div>
                      {command.subtitle && (
                        <div className="text-xs text-muted-foreground truncate">
                          {command.subtitle}
                        </div>
                      )}
                    </div>
                    {command.shortcut && (
                      <Badge variant="outline" className="text-xs">
                        {command.shortcut.join('+')}
                      </Badge>
                    )}
                  </Command.Item>
                ))}
              </Command.Group>
            ))}
          </Command.List>
        </Command>
      </DialogContent>
    </Dialog>
  );
};

export default CommandPalette;
